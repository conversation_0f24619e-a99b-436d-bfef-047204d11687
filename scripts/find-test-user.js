#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to find a user with Google authentication for testing
 */

import dotenv from 'dotenv';
import { DatabaseManager } from '../src/config/database.js';
import { UserGoogleAuth } from '../src/models/user/UserGoogleAuth.js';
import { User } from '../src/models/user/User.js';

// Load environment variables
dotenv.config();

async function findTestUser() {
  try {
    console.log('🔍 Finding users with Google authentication...');
    
    // Initialize database
    await DatabaseManager.connectAllDatabases();
    
    // Load models
    await import('../src/models/user/index.js');
    
    // Find users with Google auth
    const authRecords = await UserGoogleAuth.findAll({
      where: {
        isActive: true
      },
      limit: 5
    });

    if (authRecords.length === 0) {
      console.log('❌ No users with Google authentication found');
      console.log('Please authenticate a user with Google first');
      process.exit(1);
    }

    console.log(`✅ Found ${authRecords.length} users with Google authentication:`);
    console.log('');

    for (const record of authRecords) {
      // Get user details separately
      const user = await User.findByPk(record.userId);

      console.log(`User ID: ${record.userId}`);
      console.log(`Email: ${user?.email || 'N/A'}`);
      console.log(`Active: ${record.isActive}`);
      console.log(`Access Token Expired: ${record.isAccessTokenExpired()}`);
      console.log(`Has Refresh Token: ${!!record.refreshToken}`);
      console.log(`Granted Scopes: ${record.grantedScopes}`);
      console.log('---');
    }
    
    console.log('\nTo test Google Sheets access, run:');
    console.log(`node scripts/test-google-sheets-access.js ${authRecords[0].userId}`);
    
  } catch (error) {
    console.error('Error finding test user:', error);
    process.exit(1);
  }
}

findTestUser();
