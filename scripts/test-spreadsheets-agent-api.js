#!/usr/bin/env node

/**
 * Test script to directly test the spreadsheets-agent API
 */

import dotenv from 'dotenv';
import { DatabaseManager } from '../src/config/database.js';
import { JWTUtil } from '../src/config/jwt.js';
import { User } from '../src/models/user/User.js';
import axios from 'axios';

// Load environment variables
dotenv.config();

class SpreadsheetsAgentApiTester {
  /**
   * Generate a JWT token for testing
   */
  static async generateTestToken(userId) {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error(`User not found: ${userId}`);
    }

    const payload = {
      userId: user.id,
      email: user.email,
      mobile: user.mobile
    };

    return JWTUtil.generateToken(payload);
  }

  /**
   * Test the spreadsheets-agent API
   */
  static async testSpreadsheetsAgentApi(userId) {
    console.log(`\n🧪 Testing Spreadsheets Agent API for user: ${userId}`);
    
    try {
      // 1. Generate JWT token
      console.log('1. Generating JWT token...');
      const token = await this.generateTestToken(userId);
      console.log('   ✓ JWT token generated');

      // 2. Test the API endpoint
      const apiUrl = `http://localhost:${process.env.PORT || 5529}/api/spreadsheets-agent/query`;
      console.log(`2. Testing API endpoint: ${apiUrl}`);

      const testQueries = [
        'Show me all my spreadsheets'
      ];

      for (const query of testQueries) {
        console.log(`\n   Testing query: "${query}"`);
        
        try {
          const response = await axios.post(apiUrl, {
            query: query,
            context: {},
            sessionId: 'test-session-' + Date.now()
          }, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            timeout: 60000 // 60 second timeout
          });

          console.log('   ✓ API call successful');
          console.log(`   - Status: ${response.status}`);
          console.log(`   - Success: ${response.data.success}`);
          
          if (response.data.success) {
            console.log(`   - Message length: ${response.data.message?.length || 0} characters`);
            console.log(`   - Message preview: ${response.data.message?.substring(0, 200)}...`);
          } else {
            console.log(`   - Error: ${response.data.error}`);
            console.log(`   - Message: ${response.data.message}`);
            console.log(`   - Details: ${response.data.details}`);
          }

        } catch (apiError) {
          console.log('   ❌ API call failed');
          if (apiError.response) {
            console.log(`   - Status: ${apiError.response.status}`);
            console.log(`   - Response: ${JSON.stringify(apiError.response.data, null, 2)}`);
          } else {
            console.log(`   - Error: ${apiError.message}`);
          }
        }
      }

      console.log('\n✅ Spreadsheets Agent API test completed');
      return true;

    } catch (error) {
      console.log(`\n❌ Test failed: ${error.message}`);
      console.log(`Stack: ${error.stack}`);
      return false;
    }
  }

  /**
   * Run the tests
   */
  static async runTests() {
    console.log('🧪 Spreadsheets Agent API Tester');
    console.log('==================================');

    try {
      // Initialize database
      console.log('Initializing database...');
      await DatabaseManager.connectAllDatabases();
      console.log('✓ Database connected');

      // Load models
      await import('../src/models/user/index.js');
      console.log('✓ Models loaded');

      // Get test user ID from command line
      const testUserId = process.argv[2];
      
      if (!testUserId) {
        console.log('\n❌ Please provide a user ID as an argument:');
        console.log('node scripts/test-spreadsheets-agent-api.js <user-id>');
        console.log('\nUse this user ID from previous tests:');
        console.log('node scripts/test-spreadsheets-agent-api.js 8689180e-397e-4960-9b03-3ec69d7a23bb');
        process.exit(1);
      }

      const success = await this.testSpreadsheetsAgentApi(testUserId);
      
      if (success) {
        console.log('\n🎉 All tests completed!');
        process.exit(0);
      } else {
        console.log('\n💥 Tests failed. Check the logs above for details.');
        process.exit(1);
      }

    } catch (error) {
      console.error('Failed to run tests:', error);
      process.exit(1);
    }
  }
}

// Run the tests
SpreadsheetsAgentApiTester.runTests().catch(console.error);
