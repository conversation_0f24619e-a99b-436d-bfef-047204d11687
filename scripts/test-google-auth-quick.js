#!/usr/bin/env node

/**
 * Quick test script to identify Google authentication issues
 */

import { GoogleAuthService } from '../src/services/GoogleAuthService.js';
import { UserGoogleAuth } from '../src/models/user/index.js';
import logger from '../src/config/logger.js';

async function quickTest() {
  console.log('🔍 Quick Google Auth Test');
  console.log('=' .repeat(40));

  try {
    // Check environment variables
    console.log('1. Checking environment variables...');
    const clientId = process.env.GOOGLE_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
    const redirectUri = process.env.GOOGLE_REDIRECT_URI;

    if (!clientId || clientId.includes('your_google')) {
      console.log('❌ GOOGLE_CLIENT_ID not configured');
      return;
    }
    if (!clientSecret || clientSecret.includes('your_google')) {
      console.log('❌ GOOGLE_CLIENT_SECRET not configured');
      return;
    }
    if (!redirectUri) {
      console.log('❌ GOOGLE_REDIRECT_URI not configured');
      return;
    }
    console.log('✅ Environment variables configured');

    // Test OAuth2 client creation
    console.log('2. Testing OAuth2 client creation...');
    try {
      const oauth2Client = GoogleAuthService.createOAuth2Client();
      console.log('✅ OAuth2 client created');
    } catch (error) {
      console.log('❌ OAuth2 client creation failed:', error.message);
      return;
    }

    // Check for any authenticated users
    console.log('3. Checking for authenticated users...');
    const authRecords = await UserGoogleAuth.findAll({
      where: { isActive: true },
      limit: 5
    });

    if (authRecords.length === 0) {
      console.log('❌ No authenticated users found');
      console.log('   → Users need to authenticate with Google first');
      return;
    }

    console.log(`✅ Found ${authRecords.length} authenticated users`);

    // Test with first authenticated user
    const testUser = authRecords[0];
    console.log(`4. Testing with user: ${testUser.userId}`);

    // Check token status
    const isExpired = testUser.isAccessTokenExpired();
    console.log(`   → Access token expired: ${isExpired}`);
    console.log(`   → Has refresh token: ${!!testUser.refreshToken}`);

    // Check scopes
    const grantedScopes = testUser.grantedScopes ? testUser.grantedScopes.split(' ') : [];
    const hasSheets = grantedScopes.includes(GoogleAuthService.SCOPES.SHEETS);
    const hasDrive = grantedScopes.includes(GoogleAuthService.SCOPES.DRIVE);
    
    console.log(`   → Has Sheets scope: ${hasSheets}`);
    console.log(`   → Has Drive scope: ${hasDrive}`);

    if (!hasSheets || !hasDrive) {
      console.log('❌ Missing required scopes');
      console.log('   → User needs to re-authenticate with proper scopes');
      return;
    }

    // Test API client creation
    console.log('5. Testing API client creation...');
    try {
      const sheetsClient = await GoogleAuthService.getApiClient(testUser.userId, 'sheets');
      console.log('✅ Sheets API client created');
    } catch (error) {
      console.log('❌ Sheets API client failed:', error.message);
      return;
    }

    console.log('🎉 Basic authentication test passed!');
    console.log(`   → Test user: ${testUser.userId}`);
    console.log('   → You can now run the full debug script with this user ID');

  } catch (error) {
    console.error('💥 Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

quickTest()
  .then(() => process.exit(0))
  .catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });
