#!/usr/bin/env node

/**
 * Test script to debug Google Sheets API access issues
 * This script will test the complete flow from authentication to API access
 */

import dotenv from 'dotenv';
import { DatabaseManager } from '../src/config/database.js';
import { GoogleAuthService } from '../src/services/GoogleAuthService.js';
import { DriveUtilityApp } from '../src/services/DriveUtilityApp.js';
import { UserGoogleAuth } from '../src/models/user/UserGoogleAuth.js';
import { User } from '../src/models/user/User.js';
import logger from '../src/config/logger.js';

// Load environment variables
dotenv.config();

class GoogleSheetsAccessTester {
  /**
   * Test Google Sheets API access for a specific user
   */
  static async testUserAccess(userId) {
    console.log(`\n🔍 Testing Google Sheets access for user: ${userId}`);
    
    try {
      // 1. Check if user exists
      console.log('1. Checking if user exists...');
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error(`User not found: ${userId}`);
      }
      console.log('   ✓ User found');

      // 2. Check Google auth record
      console.log('2. Checking Google auth record...');
      const authRecord = await UserGoogleAuth.findByUserId(userId);
      if (!authRecord) {
        throw new Error('No Google auth record found');
      }
      console.log('   ✓ Google auth record found');
      console.log(`   - Active: ${authRecord.isActive}`);
      console.log(`   - Access token expired: ${authRecord.isAccessTokenExpired()}`);
      console.log(`   - Has refresh token: ${!!authRecord.refreshToken}`);
      console.log(`   - Granted scopes: ${authRecord.grantedScopes}`);

      // 3. Check authentication status
      console.log('3. Checking authentication status...');
      const authStatus = await GoogleAuthService.getAuthStatus(userId);
      console.log('   ✓ Auth status retrieved');
      console.log(`   - Authenticated: ${authStatus.isAuthenticated}`);
      console.log(`   - Token expired: ${authStatus.isAccessTokenExpired}`);
      console.log(`   - Has refresh token: ${authStatus.hasRefreshToken}`);
      console.log(`   - Granted scopes: ${authStatus.grantedScopes?.join(', ')}`);

      // 4. Check required scopes
      console.log('4. Checking required scopes...');
      const hasRequiredScopes = await GoogleAuthService.hasRequiredScopes(userId, [
        GoogleAuthService.SCOPES.SHEETS
      ]);
      console.log(`   - Has Sheets scope: ${hasRequiredScopes}`);
      
      if (!hasRequiredScopes) {
        console.log(`   ❌ Missing required scope: ${GoogleAuthService.SCOPES.SHEETS}`);
        console.log(`   Available scopes: ${authStatus.grantedScopes?.join(', ')}`);
        return false;
      }
      console.log('   ✓ Required scopes available');

      // 5. Test getting authenticated client
      console.log('5. Testing authenticated client creation...');
      const authClient = await GoogleAuthService.getAuthenticatedClient(userId);
      console.log('   ✓ Authenticated client created');

      // 6. Test getting Sheets API client
      console.log('6. Testing Sheets API client creation...');
      const sheetsClient = await GoogleAuthService.getApiClient(userId, 'sheets');
      console.log('   ✓ Sheets API client created');

      // 7. Test DriveUtilityApp permissions validation
      console.log('7. Testing DriveUtilityApp permissions validation...');
      await DriveUtilityApp._validateSheetsPermissions(userId);
      console.log('   ✓ DriveUtilityApp permissions validation passed');

      // 8. Test getting Sheets client through DriveUtilityApp
      console.log('8. Testing DriveUtilityApp Sheets client...');
      const driveUtilityClient = await DriveUtilityApp._getSheetsClient(userId);
      console.log('   ✓ DriveUtilityApp Sheets client created');

      // 9. Test actual API call - list spreadsheets
      console.log('9. Testing actual Google Sheets API call...');
      try {
        const result = await DriveUtilityApp.getAllSpreadsheets(userId, { pageSize: 5 });
        console.log('   ✓ Google Sheets API call successful');
        console.log(`   - Found ${result.spreadsheets?.length || 0} spreadsheets`);
        
        if (result.spreadsheets && result.spreadsheets.length > 0) {
          console.log('   - Sample spreadsheet:', {
            id: result.spreadsheets[0].id,
            name: result.spreadsheets[0].name
          });
        }
      } catch (apiError) {
        console.log('   ❌ Google Sheets API call failed');
        console.log(`   Error: ${apiError.message}`);
        console.log(`   Stack: ${apiError.stack}`);
        return false;
      }

      console.log('\n✅ All tests passed! Google Sheets access is working correctly.');
      return true;

    } catch (error) {
      console.log(`\n❌ Test failed: ${error.message}`);
      console.log(`Stack: ${error.stack}`);
      return false;
    }
  }

  /**
   * Test with a sample user (you'll need to provide a real user ID)
   */
  static async runTests() {
    console.log('🧪 Google Sheets Access Tester');
    console.log('================================');

    try {
      // Initialize database
      console.log('Initializing database...');
      await DatabaseManager.connectAllDatabases();
      console.log('✓ Database connected');

      // Load models
      await import('../src/models/user/index.js');
      console.log('✓ Models loaded');

      // You need to replace this with an actual user ID that has Google auth
      const testUserId = process.argv[2];
      
      if (!testUserId) {
        console.log('\n❌ Please provide a user ID as an argument:');
        console.log('node scripts/test-google-sheets-access.js <user-id>');
        process.exit(1);
      }

      const success = await this.testUserAccess(testUserId);
      
      if (success) {
        console.log('\n🎉 All tests completed successfully!');
        process.exit(0);
      } else {
        console.log('\n💥 Tests failed. Check the logs above for details.');
        process.exit(1);
      }

    } catch (error) {
      console.error('Failed to run tests:', error);
      process.exit(1);
    }
  }
}

// Run the tests
GoogleSheetsAccessTester.runTests().catch(console.error);
