#!/usr/bin/env node

/**
 * Debug script for Google Sheets authentication issues
 * This script helps identify where the authentication is failing in the SpreadsheetsAgent workflow
 */

import { GoogleAuthService } from '../src/services/GoogleAuthService.js';
import { DriveUtilityApp } from '../src/services/DriveUtilityApp.js';
import { UserGoogleAuth } from '../src/models/user/index.js';
import logger from '../src/config/logger.js';

// Test user ID - replace with actual user ID experiencing issues
const TEST_USER_ID = process.argv[2];

if (!TEST_USER_ID) {
  console.error('Usage: node scripts/debug-google-sheets-auth.js <userId>');
  process.exit(1);
}

async function debugGoogleSheetsAuth() {
  console.log('🔍 Starting Google Sheets Authentication Debug');
  console.log('=' .repeat(60));
  console.log(`Testing for User ID: ${TEST_USER_ID}`);
  console.log('');

  try {
    // Step 0: Check environment variables
    console.log('0️⃣  Checking Environment Variables...');
    const requiredEnvVars = ['GOOGLE_CLIENT_ID', 'GOOGLE_CLIENT_SECRET', 'GOOGLE_REDIRECT_URI'];
    let envVarsOk = true;

    requiredEnvVars.forEach(envVar => {
      const value = process.env[envVar];
      if (!value || value === 'your_google_oauth_client_id_here' || value === 'your_google_oauth_client_secret_here') {
        console.log(`❌ ${envVar} is not properly configured`);
        envVarsOk = false;
      } else {
        console.log(`✅ ${envVar} is configured (${value.substring(0, 10)}...)`);
      }
    });

    if (!envVarsOk) {
      console.log('❌ Google OAuth environment variables are not properly configured');
      console.log('   → Please check your .env file and ensure Google OAuth credentials are set');
      return;
    }
    console.log('');

    // Test OAuth2 client creation
    console.log('   → Testing OAuth2 client creation...');
    try {
      const oauth2Client = GoogleAuthService.createOAuth2Client();
      console.log('✅ OAuth2 client created successfully');
    } catch (error) {
      console.log('❌ Failed to create OAuth2 client');
      console.log(`   → Error: ${error.message}`);
      return;
    }
    console.log('');
    // Step 1: Check if user has Google auth record
    console.log('1️⃣  Checking Google Auth Record...');
    const authRecord = await UserGoogleAuth.findByUserId(TEST_USER_ID);
    
    if (!authRecord) {
      console.log('❌ No Google auth record found for user');
      console.log('   → User needs to authenticate with Google first');
      return;
    }
    
    console.log('✅ Google auth record found');
    console.log(`   → Created: ${authRecord.createdAt}`);
    console.log(`   → Last Auth: ${authRecord.lastAuthenticatedAt}`);
    console.log(`   → Is Active: ${authRecord.isActive}`);
    console.log('');

    // Step 2: Check token status
    console.log('2️⃣  Checking Token Status...');
    const isAccessTokenExpired = authRecord.isAccessTokenExpired();
    const hasRefreshToken = !!authRecord.refreshToken;
    
    console.log(`   → Access Token Expired: ${isAccessTokenExpired}`);
    console.log(`   → Has Refresh Token: ${hasRefreshToken}`);
    console.log(`   → Access Token Expires At: ${authRecord.accessTokenExpiresAt}`);
    
    if (isAccessTokenExpired && !hasRefreshToken) {
      console.log('❌ Access token expired and no refresh token available');
      console.log('   → User needs to re-authenticate with Google');
      return;
    }
    console.log('');

    // Step 3: Check granted scopes
    console.log('3️⃣  Checking Granted Scopes...');
    const grantedScopes = authRecord.grantedScopes ? authRecord.grantedScopes.split(' ') : [];
    const requiredSheetsScope = GoogleAuthService.SCOPES.SHEETS;
    const requiredDriveScope = GoogleAuthService.SCOPES.DRIVE;
    
    console.log(`   → Granted Scopes: ${grantedScopes.length} scopes`);
    grantedScopes.forEach(scope => console.log(`     - ${scope}`));
    console.log('');
    console.log(`   → Required Sheets Scope: ${requiredSheetsScope}`);
    console.log(`   → Has Sheets Scope: ${grantedScopes.includes(requiredSheetsScope)}`);
    console.log(`   → Required Drive Scope: ${requiredDriveScope}`);
    console.log(`   → Has Drive Scope: ${grantedScopes.includes(requiredDriveScope)}`);
    
    if (!grantedScopes.includes(requiredSheetsScope)) {
      console.log('❌ Missing required Google Sheets scope');
      console.log('   → User needs to re-authenticate with proper scopes');
      return;
    }
    
    if (!grantedScopes.includes(requiredDriveScope)) {
      console.log('❌ Missing required Google Drive scope');
      console.log('   → User needs to re-authenticate with proper scopes');
      return;
    }
    console.log('');

    // Step 4: Test authenticated client creation
    console.log('4️⃣  Testing Authenticated Client Creation...');
    try {
      const authClient = await GoogleAuthService.getAuthenticatedClient(TEST_USER_ID);
      console.log('✅ Authenticated client created successfully');
      
      // Check if token was refreshed
      await authRecord.reload();
      console.log(`   → Token refresh check: ${authRecord.lastAuthenticatedAt}`);
    } catch (error) {
      console.log('❌ Failed to create authenticated client');
      console.log(`   → Error: ${error.message}`);
      return;
    }
    console.log('');

    // Step 5: Test Google Sheets API client
    console.log('5️⃣  Testing Google Sheets API Client...');
    try {
      const sheetsClient = await GoogleAuthService.getApiClient(TEST_USER_ID, 'sheets');
      console.log('✅ Sheets API client created successfully');
    } catch (error) {
      console.log('❌ Failed to create Sheets API client');
      console.log(`   → Error: ${error.message}`);
      return;
    }
    console.log('');

    // Step 6: Test Google Drive API client
    console.log('6️⃣  Testing Google Drive API Client...');
    try {
      const driveClient = await GoogleAuthService.getApiClient(TEST_USER_ID, 'drive');
      console.log('✅ Drive API client created successfully');
    } catch (error) {
      console.log('❌ Failed to create Drive API client');
      console.log(`   → Error: ${error.message}`);
      return;
    }
    console.log('');

    // Step 7: Test DriveUtilityApp permissions validation
    console.log('7️⃣  Testing DriveUtilityApp Permissions Validation...');
    try {
      await DriveUtilityApp._validateSheetsPermissions(TEST_USER_ID);
      console.log('✅ DriveUtilityApp permissions validation passed');
    } catch (error) {
      console.log('❌ DriveUtilityApp permissions validation failed');
      console.log(`   → Error: ${error.message}`);
      return;
    }
    console.log('');

    // Step 8: Test actual Google Sheets API call
    console.log('8️⃣  Testing Actual Google Sheets API Call...');
    try {
      const result = await DriveUtilityApp.getAllSpreadsheets(TEST_USER_ID, { pageSize: 5 });
      console.log('✅ Google Sheets API call successful');
      console.log(`   → Found ${result.spreadsheets.length} spreadsheets`);
      
      if (result.spreadsheets.length > 0) {
        console.log('   → Sample spreadsheets:');
        result.spreadsheets.slice(0, 3).forEach((sheet, index) => {
          console.log(`     ${index + 1}. ${sheet.name} (${sheet.id})`);
        });
      }
    } catch (error) {
      console.log('❌ Google Sheets API call failed');
      console.log(`   → Error: ${error.message}`);
      console.log(`   → Error Code: ${error.code || 'N/A'}`);
      console.log(`   → Error Stack: ${error.stack}`);
      return;
    }
    console.log('');

    // Step 9: Test SpreadsheetsAgent tool directly
    console.log('9️⃣  Testing SpreadsheetsAgent Tool Directly...');
    try {
      // Import and test the agent tool
      const { SpreadsheetsAgent } = await import('../src/services/SpreadsheetsAgent.js');
      const agent = new SpreadsheetsAgent();
      
      // Get the list_sheets tool
      const listSheetsToolIndex = agent.tools.findIndex(tool => tool.name === 'list_sheets');
      if (listSheetsToolIndex === -1) {
        console.log('❌ list_sheets tool not found in agent');
        return;
      }
      
      const listSheetsTool = agent.tools[listSheetsToolIndex];
      const toolResult = await listSheetsTool.invoke({ userId: TEST_USER_ID });
      
      console.log('✅ SpreadsheetsAgent list_sheets tool executed successfully');
      console.log(`   → Tool Result: ${toolResult.substring(0, 200)}...`);
      
      // Parse the result to check for errors
      try {
        const parsedResult = JSON.parse(toolResult);
        if (parsedResult.error) {
          console.log('❌ Tool returned error result');
          console.log(`   → Error: ${parsedResult.error}`);
        } else if (parsedResult.sheets) {
          console.log(`   → Successfully retrieved ${parsedResult.sheets.length} sheets`);
        }
      } catch (parseError) {
        console.log('⚠️  Could not parse tool result as JSON');
      }
    } catch (error) {
      console.log('❌ SpreadsheetsAgent tool test failed');
      console.log(`   → Error: ${error.message}`);
      return;
    }
    console.log('');

    console.log('🎉 All tests passed! Google Sheets authentication is working correctly.');
    
  } catch (error) {
    console.error('💥 Unexpected error during debug:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Run the debug function
debugGoogleSheetsAuth()
  .then(() => {
    console.log('\n🏁 Debug completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Debug failed:', error);
    process.exit(1);
  });
