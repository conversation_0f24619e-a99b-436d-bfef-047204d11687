/**
 * @fileoverview SpreadsheetsAgentService - Integration service for SpreadsheetsAgent
 */

import { SpreadsheetsAgent } from './SpreadsheetsAgent.js';
import { CreditService } from './CreditService.js';
import { CREDIT_SYSTEM } from '../utils/constants.js';
import logger from '../config/logger.js';

/**
 * SpreadsheetsAgentService Class
 * Provides integration layer for SpreadsheetsAgent with the application
 */
export class SpreadsheetsAgentService {
  constructor() {
    this.agent = new SpreadsheetsAgent();
  }

  /**
   * Process a spreadsheet request with credit checking
   * @param {string} userId - User ID
   * @param {string} message - User message/request
   * @param {Object} context - Additional context
   * @param {string} sessionId - Optional session ID for tracking
   * @returns {Promise<Object>} Agent response
   */
  async processRequest(userId, message, context = {}, sessionId = null) {
    try {
      logger.debug(`SpreadsheetsAgentService processing request for user: ${userId}`, {
        message: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
        context,
        sessionId
      });

      // Check if user has enough credits
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        logger.warn(`Insufficient credits for user: ${userId}`);
        return {
          success: false,
          error: 'INSUFFICIENT_CREDITS',
          message: 'Insufficient credits to process this request. Please upgrade your plan or purchase credits.',
          creditsRequired: CREDIT_SYSTEM.CHAT_MESSAGE_COST
        };
      }

      // Process the request through the agent
      logger.debug(`Calling agent.processRequest for user: ${userId}`);
      const result = await this.agent.processRequest(userId, message, context, sessionId);

      logger.debug(`Agent response for user: ${userId}`, {
        success: result.success,
        hasMessage: !!result.message,
        error: result.error,
        messageLength: result.message?.length
      });

      // Deduct credits if the request was successful
      if (result.success !== false) {
        try {
          await CreditService.deductCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST, 'Spreadsheet Agent Request');
          logger.info(`Deducted ${CREDIT_SYSTEM.CHAT_MESSAGE_COST} credits for user ${userId} - Spreadsheet Agent`);
        } catch (creditError) {
          logger.error('Error deducting credits:', creditError);
          // Don't fail the request if credit deduction fails, just log it
        }
      }

      return result;
    } catch (error) {
      logger.error('Error in SpreadsheetsAgentService:', {
        userId,
        message: message?.substring(0, 100),
        error: error.message,
        stack: error.stack,
        name: error.name
      });
      return {
        success: false,
        error: 'SERVICE_ERROR',
        message: 'An error occurred while processing your spreadsheet request.',
        details: error.message
      };
    }
  }

  /**
   * Get all spreadsheets for a user
   * @param {string} userId - User ID
   * @param {Object} options - Options for filtering/pagination
   * @returns {Promise<Object>} List of spreadsheets
   */
  async getAllSpreadsheets(userId, options = {}) {
    const message = 'Get all my spreadsheets';
    const context = { action: 'get_all_spreadsheets', ...options };
    return await this.processRequest(userId, message, context);
  }

  /**
   * Get details of a specific spreadsheet
   * @param {string} userId - User ID
   * @param {string} spreadsheetId - Spreadsheet ID
   * @returns {Promise<Object>} Spreadsheet details
   */
  async getSpreadsheetDetails(userId, spreadsheetId) {
    const message = `Get details of spreadsheet ${spreadsheetId}`;
    const context = { action: 'get_spreadsheet_details', spreadsheetId };
    return await this.processRequest(userId, message, context);
  }

  /**
   * Read content from a spreadsheet
   * @param {string} userId - User ID
   * @param {string} spreadsheetId - Spreadsheet ID
   * @param {string} sheetName - Sheet name (optional)
   * @param {string} range - Range to read (optional)
   * @returns {Promise<Object>} Spreadsheet content
   */
  async readSpreadsheetContent(userId, spreadsheetId, sheetName = null, range = null) {
    let message = `Read content from spreadsheet ${spreadsheetId}`;
    if (sheetName) message += ` from sheet ${sheetName}`;
    if (range) message += ` in range ${range}`;

    const context = { 
      action: 'read_spreadsheet_content', 
      spreadsheetId, 
      sheetName, 
      range 
    };
    return await this.processRequest(userId, message, context);
  }

  /**
   * Update content in a spreadsheet
   * @param {string} userId - User ID
   * @param {string} spreadsheetId - Spreadsheet ID
   * @param {string} sheetName - Sheet name
   * @param {string} range - Range to update
   * @param {Array} values - 2D array of values
   * @returns {Promise<Object>} Update result
   */
  async updateSpreadsheetContent(userId, spreadsheetId, sheetName, range, values) {
    const message = `Update content in spreadsheet ${spreadsheetId}, sheet ${sheetName}, range ${range}`;
    const context = { 
      action: 'update_spreadsheet_content', 
      spreadsheetId, 
      sheetName, 
      range, 
      values 
    };
    return await this.processRequest(userId, message, context);
  }

  /**
   * Create a new spreadsheet
   * @param {string} userId - User ID
   * @param {string} title - Spreadsheet title
   * @param {Object} options - Creation options
   * @returns {Promise<Object>} Created spreadsheet
   */
  async createSpreadsheet(userId, title, options = {}) {
    const message = `Create a new spreadsheet titled "${title}"`;
    const context = { 
      action: 'create_spreadsheet', 
      title, 
      ...options 
    };
    return await this.processRequest(userId, message, context);
  }

  /**
   * Process natural language spreadsheet requests
   * @param {string} userId - User ID
   * @param {string} naturalLanguageRequest - Natural language request
   * @returns {Promise<Object>} Agent response
   */
  async processNaturalLanguageRequest(userId, naturalLanguageRequest) {
    return await this.processRequest(userId, naturalLanguageRequest);
  }

  /**
   * Get agent status
   * @returns {Object} Agent status
   */
  getStatus() {
    return this.agent.getStatus();
  }

  /**
   * Reset agent (useful for testing)
   */
  reset() {
    this.agent.reset();
  }

  /**
   * Get available operations
   * @returns {Array} List of available operations
   */
  getAvailableOperations() {
    return [
      {
        name: 'getAllSpreadsheets',
        description: 'Get all spreadsheets accessible to the user',
        parameters: ['userId', 'options?']
      },
      {
        name: 'getSpreadsheetDetails',
        description: 'Get detailed information about a specific spreadsheet',
        parameters: ['userId', 'spreadsheetId']
      },
      {
        name: 'readSpreadsheetContent',
        description: 'Read content from a spreadsheet',
        parameters: ['userId', 'spreadsheetId', 'sheetName?', 'range?']
      },
      {
        name: 'updateSpreadsheetContent',
        description: 'Update content in a spreadsheet',
        parameters: ['userId', 'spreadsheetId', 'sheetName', 'range', 'values']
      },
      {
        name: 'createSpreadsheet',
        description: 'Create a new spreadsheet',
        parameters: ['userId', 'title', 'options?']
      },
      {
        name: 'processNaturalLanguageRequest',
        description: 'Process natural language spreadsheet requests',
        parameters: ['userId', 'naturalLanguageRequest']
      }
    ];
  }
}

// Export singleton instance
export const spreadsheetsAgentService = new SpreadsheetsAgentService();
